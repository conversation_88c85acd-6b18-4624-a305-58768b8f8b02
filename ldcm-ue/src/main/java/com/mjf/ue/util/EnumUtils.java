package com.mjf.ue.util;

import com.mjf.ue.enums.DictEnum;

public class EnumUtils {
    public static <E extends Enum<E> & DictEnum> String getLabelByCode(Class<E> enumClass, String code) {
        for (E e : enumClass.getEnumConstants()) {
            if (e.getCode() == code) {
                return e.getLabel();
            }
        }
        return null;
    }

    public static <E extends Enum<E> & DictEnum> String getCodeByLabel(Class<E> enumClass, String label) {
        for (E e : enumClass.getEnumConstants()) {
            if (e.getLabel().equals(label)) {
                return e.getCode();
            }
        }
        return null;
    }
}