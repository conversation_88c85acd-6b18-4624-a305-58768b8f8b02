package com.mjf.ue.enums;

public enum U8VendortypeEnum {
    K01Type("K01","中央企业"),
    K02Type("K02","地方国有企业"),
    K03Type("K03","中央部委"),
    K04Type("K04","地方政府"),
    K05Type("K05","民营企业"),
    K06Type("K06","其他"),
    K07Type("K07","事业单位"),
    K08Type("K08","个体工商户"),
    ;

    private final String code;
    private final String label;

    U8VendortypeEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }

    public static String getLabelByCode(String code) {
        for (U8VendortypeEnum e : values()) {
            if (e.getCode() == code) {
                return e.getLabel();
            }
        }
        return null;
    }

    public static String getCodeByLabel(String label) {
        for (U8VendortypeEnum e : values()) {
            if (e.getLabel() == label) {
                return e.getCode();
            }
        }
        return null;
    }
}
