package com.mjf.ue.enums;

public enum U8BillingTypeEnum {
    N01Type("N01", "收款"),
    N02Type("N02", "付款"),
    N03Type("N03", "无收无支"),
    ;

    private final String code;
    private final String label;

    U8BillingTypeEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }
}
