package com.mjf.ue.enums;

public enum U8SubcompanyEnum {
    NBBZ("T00","集团内部标识"),
    WBBZ("T01","集团内部标识"),
            ;

    private final String code;
    private final String label;

    U8SubcompanyEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }

    public static String getLabelByCode(String code) {
        for (U8SubcompanyEnum e : values()) {
            if (e.getCode() == code) {
                return e.getLabel();
            }
        }
        return null;
    }

    public static String getCodeByLabel(String label) {
        for (U8SubcompanyEnum e : values()) {
            if (e.getLabel() == label) {
                return e.getCode();
            }
        }
        return null;
    }
}
