package com.mjf.ue.enums;

public enum U8FrameworkContractEnum {
    T00Type("T00", "非框架合同"),
    T01Type("T01", "框架合同"),
    ;

    private final String code;
    private final String label;

    U8FrameworkContractEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }
}


