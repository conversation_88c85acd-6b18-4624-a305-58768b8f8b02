package com.mjf.ue.mapper;

import com.mjf.ue.domain.U8ContractEntity;
import com.mjf.ue.domain.U8CubasdocEntity;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface U8CustomersMapper {

    @Select("select a.cCusCode as cvencode,a.cCusName as ccargsname,a.cCusRegCode as ccarvendorcode,a.cCus<PERSON><PERSON> as ccarlegalperson,a.cReg<PERSON><PERSON> as ccarregcapital,a.dDepBeginDate as ccarsetdate,a.ccodeID as ccarvendoraddress,a.bCusDomestic as ccarcountry,cCusDefine10 as ccarsubcompany,a.cCusDefine9 as ccarvendortype,a.cTrade as ccarvendorcategory from customer a,HR_CT007 b where a.cCusCode in ('990372') and a.cCusDefine8 = b.vdescription")
    List<U8ContractEntity> getCustomers();

    @Select("select a.cVenCode as cvencode,a.c<PERSON>en<PERSON><PERSON> as ccargsname,a.c<PERSON>en<PERSON>egC<PERSON> as ccarvendorcode,a.c<PERSON><PERSON><PERSON> as ccarlegalperson,a.fRegistFund as ccarregcapital,a.cVenDefine15 as ccarsetdate,b.ccodeID as ccarvendoraddress,a.bVenOverseas as ccarcountry,cVenDefine10 as ccarsubcompany,a.cVenDefine9 as ccarvendortype,a.cTrade as ccarvendorcategory from vendor a,HR_CT007 b where a.cvencode in ('990372') and a.cvenDefine8 = b.vdescription")
    List<U8ContractEntity> getSupplier();
}
